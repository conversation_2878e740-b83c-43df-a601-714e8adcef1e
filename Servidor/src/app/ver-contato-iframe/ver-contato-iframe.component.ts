import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild} from '@angular/core';
import {SelosComponent} from "../componentes/selo/selo.component";
import {DropDownFilterSettings} from "@progress/kendo-angular-dropdowns";
import {ContatosService} from "../services/contatos.service";
import {ActivatedRoute, Router} from "@angular/router";
import {Location} from "@angular/common";
import {AutorizacaoService} from "../services/autorizacao.service";

import {ConstantsService} from "../fidelidade/ConstantsService";
import {ArmazenamentoService} from "../services/armazenamento.service";
import {BotsService} from "../services/bots.service";
import {Subscription} from "rxjs";
import {DiagnosticoWhatsappService} from "../services/diagnostico-whatsapp.service";

// Interface para os itens do log
interface LogItem {
  timestamp: Date;
  tempoRelativo: string;
  tipo: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS';
  categoria: 'PROCESSAMENTO' | 'VALIDACAO' | 'ENVIO' | 'SISTEMA';
  icone: string;
  iconeCategoria: string;
  nome: string;
  telefone: string;
  mensagem: string;
  textoMensagem?: string;
  tipoMensagem?: string;
  detalhes?: any;
  detalhesFormatados?: string;
  cssClass: string;
  expanded?: boolean;
}

@Component({
  selector: 'app-ver-contato-iframe',
  templateUrl: './ver-contato-iframe.component.html',
  styleUrls: ['./ver-contato-iframe.component.scss']
})
export class VerContatoIframeComponent implements OnInit, OnDestroy {
  acoes: any = [];
  cartoes: any = [];
  filtro: any = {cartao: null};
  contato: any = null;
  @ViewChild('selosCartao', { static: true } ) selosCartao: SelosComponent;
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'contains'
  };
  abraDialogo: boolean;
  telefone: any;
  nomeContato: any;
  widget = false;
  usuario: any;
  empresa: any;
  pedidosEmAberto: any[];
  pedidosAnteriores: any[];
  carregou: boolean;
  novoPedido: any = {}
  msg: any = '';
  dev = false;
  assumirComandoBot: any;
  atendente = false;
  desativarParaSempre = false;
  statusMia = false;
  contatoPorDados: any = {};
  assinante: Subscription;

  // Propriedades para controlar a exibição do relatório na tela
  exibirRelatorioNaTela = false;
  conteudoRelatorio = '';
  conteudoRelatorioFormatado = '';
  tituloRelatorio = '';
  dataGeracaoRelatorio = '';
  
  // Propriedades para o ListView
  logsListView: LogItem[] = [];
  logsListViewFiltrados: LogItem[] = [];
  filtroTipo: string = 'TODOS';
  filtroCategoria: string = 'TODAS';
  textoBusca: string = '';
  totalLogs: number = 0;
  estatisticas = {
    sucessos: 0,
    erros: 0,
    avisos: 0,
    processamentos: 0
  };

  constructor(  private  contatosService: ContatosService, private router: Router,
               private activatedRoute: ActivatedRoute, private _location: Location,
               private autorizacaoService: AutorizacaoService, private constantsService: ConstantsService,
               private armazenamentoService: ArmazenamentoService, private botsService: BotsService,
               private diagnosticoWhatsappService: DiagnosticoWhatsappService) {
    this.dev = (window.location.href.indexOf('localhost') !== -1);
  }

  inIframe () {
    try {
      return window.self !== window.top;
    } catch (e) {
      return true;
    }
  }

  calculeSeBotEstahAtivo(telefone: string) {
    if( !this.usuario || !telefone ) {
      return;
    }

    this.botsService.estahComAtedente(this.empresa, telefone).then( (respSessao: any) => {
      this.atendente = respSessao.data.atendente;
      this.desativarParaSempre = respSessao.data.desativarParaSempre;

      const configuracoesMia = respSessao.data.mia;

      if( configuracoesMia ) {
        this.statusMia = (configuracoesMia.status !== 'DESATIVADA');
      }
    });
  }

  ngOnInit() {
    this.msg = this.activatedRoute.snapshot.queryParams.msg;
    this.novoPedido = { guid: this.activatedRoute.snapshot.queryParams.codigo};

    let usuarioLogado = this.autorizacaoService.getUser();

    if( usuarioLogado != null )
      this.usuario = usuarioLogado;
    else {
      this.usuario = {};
    }

    this.autorizacaoService.usuarioLogado$.subscribe( (usuario) => {
      if( !usuario ) return;

      this.usuario = usuario;
    });

    const gerarLinkPagamento  = this.activatedRoute.snapshot.queryParams.gmlkpg;

    //gerar msg link pagamento
    if(gerarLinkPagamento)
      window['root'].envieMensagemLinkPagamento(gerarLinkPagamento);



    this.constantsService.empresa$.subscribe( (empresa) => {
      if( !empresa ) { return; }

      this.empresa = empresa;

      this.assinante = this.botsService.eventoEstadoMia$.subscribe( (dados: any) => {
        if( this.contato ) {
          this.calculeSeBotEstahAtivo(this.contato.codigoPais + this.contato.telefone);
        }
      });

      this.activatedRoute.queryParams.subscribe(queryParams => {
        this.nomeContato = this.activatedRoute.snapshot.queryParams.nome;

        if( this.nomeContato === 'undefined' || this.nomeContato === undefined ) {
          this.nomeContato = '';
        }

        this.widget = this.inIframe();

        const { contato } = window.history.state;

        if (contato) {
          this.setContato(contato);
        }
        else {
          this.telefone = decodeURIComponent(this.activatedRoute.snapshot.params['zap']);
          this.contatoPorDados = this.gereNovoContato(this.nomeContato, this.telefone)
          this.contatosService.obtenhaContato(this.telefone.toString()).then( (objContato: any) => {
            this.setContato(objContato);
          });
        }

        this.calculeSeBotEstahAtivo(this.activatedRoute.snapshot.params['zap']);
      });
    });
  }

  salveContato(contato: any) {
    this.contato = contato;
  }

  gereNovoContato(nomeContato: string, telefoneCompleto: string) {

    const { codigoPais, telefone } = this.contatosService.extraiCodigoPaisETelefone(telefoneCompleto)

    return { nome: nomeContato,
      codigoPais: codigoPais,
      telefone: telefone
    }
  }


  setContato(contato: any) {
    this.contato = contato;

    let estadoAnterior: any = JSON.parse(this.armazenamentoService.carregue('url_' + this.telefone))  ;

    if(estadoAnterior) {
      this.router.navigateByUrl(estadoAnterior.url, {state: estadoAnterior.state});
      return;
    }

    this.carregou = true;
    if( !contato ) {
      return;
    }

    this.cartoes = contato.cartoes;
    this.cartoes.forEach( cartao => cartao.descricao = cartao.plano.nome)
  }

  voltar() {
    this._location.back();
  }

  recarregue() {
    window.location.reload();
  }

  imprimaPedido(pedido: any) {
    window.open('/imprimir/pedido/' + pedido.guid);
  }

  digitouMensagemBot($event: any) {
    const mensagem = $event.target.value;

    window['root'].novasMensagens([mensagem]);

    $event.target.value = '';
  }

  alterouStatusBot() {

  }

  executarDiagnostico() {
    if (!this.contato) {
      this.exibirRelatorio('🩺 Diagnóstico', 'Nenhum contato carregado para diagnóstico.');
      return;
    }

    const titulo = '🩺 Diagnóstico do Contato';
    const telefoneCompleto = (this.contato.codigoPais || '') + this.contato.telefone;
    const logs = this.diagnosticoWhatsappService.obterLogsPorTelefone(telefoneCompleto);
    
    if (logs.length === 0) {
      this.exibirRelatorio(titulo, `Nenhum log encontrado para o telefone ${telefoneCompleto}`);
      return;
    }
    
    this.exibirRelatorioListView(titulo, logs);
  }

  private coletarInformacoesDiagnostico(): any {
    const diagnostico: any = {
      timestamp: new Date().toLocaleString('pt-BR'),
      contato: {},
      whatsapp: {},
      mia: {},
      sistema: {},
      logsWhatsapp: {}
    };

    // Informações do contato
    if (this.contato) {
      diagnostico.contato = {
        id: this.contato.id || 'Não definido',
        nome: this.contato.nome || 'Não informado',
        telefone: this.contato.telefone || 'Não informado',
        codigoPais: this.contato.codigoPais || 'Não informado',
        telefoneCompleto: (this.contato.codigoPais || '') + (this.contato.telefone || ''),
        status: this.contato.status || 'Não definido',
        email: this.contato.email || 'Não informado',
        bloqueado: this.contato.bloqueado ? 'Sim' : 'Não',
        dataAtivacao: this.contato.dataAtivacao || 'Não definido',
        ultimaVisita: this.contato.ultimaVisita || 'Não definido',
        quantidadeCartoes: this.contato.cartoes ? this.contato.cartoes.length : 0,
        tags: this.contato.tags ? this.contato.tags.map((tag: any) => tag.nome).join(', ') : 'Nenhuma'
      };
    }

    // Informações do WhatsApp/Mia
    diagnostico.mia = {
      statusMia: this.statusMia ? 'Ativa' : 'Inativa',
      atendente: this.atendente ? 'Sim' : 'Não',
      desativarParaSempre: this.desativarParaSempre ? 'Sim' : 'Não'
    };

    // Informações do sistema
    diagnostico.sistema = {
      carregou: this.carregou ? 'Sim' : 'Não',
      widget: this.widget ? 'Sim' : 'Não',
      dev: this.dev ? 'Sim' : 'Não',
      empresaId: this.empresa?.id || 'Não definido',
      empresaNome: this.empresa?.nome || 'Não definido',
      usuarioId: this.usuario?.id || 'Não definido',
      usuarioNome: this.usuario?.nome || 'Não definido'
    };

    // Informações do WhatsApp (se disponível)
    diagnostico.whatsapp = {
      numeroWhatsapp: (window as any)['numeroWhatsapp'] || 'Não disponível',
      carregouWhatsapp: (window as any)['carregouWhatsapp'] || false,
      inIframe: this.inIframe() ? 'Sim' : 'Não'
    };

    // Logs específicos do WhatsApp para este contato
    if (this.contato?.telefone) {
      const telefoneCompleto = (this.contato.codigoPais || '') + this.contato.telefone;
      const logsContato = this.diagnosticoWhatsappService.obterLogsPorTelefone(telefoneCompleto);
      const estatisticas = this.diagnosticoWhatsappService.obterEstatisticas(telefoneCompleto);

      diagnostico.logsWhatsapp = {
        totalLogs: logsContato.length,
        ultimoLog: logsContato[0]?.timestamp || 'Nenhum',
        estatisticas: estatisticas,
        logsRecentes: logsContato.slice(0, 5).map(log => ({
          timestamp: log.timestamp.toLocaleTimeString('pt-BR'),
          tipo: log.tipo,
          categoria: log.categoria,
          mensagem: log.mensagem
        }))
      };
    } else {
      diagnostico.logsWhatsapp = {
        totalLogs: 0,
        ultimoLog: 'N/A - Contato sem telefone',
        estatisticas: null,
        logsRecentes: []
      };
    }

    return diagnostico;
  }



  private obterEmojiPorTipo(tipo: string): string {
    const emojis: any = {
      'INFO': 'ℹ️',
      'WARNING': '⚠️',
      'ERROR': '❌',
      'SUCCESS': '✅'
    };
    return emojis[tipo] || 'ℹ️';
  }



  /**
   * Método para exibir TODOS os logs do sistema (debug)
   */
  exibirTodosLogs(): void {
    const titulo = '🔍 Todos os Logs do Sistema (Debug)';
    const logs = this.diagnosticoWhatsappService.obterTodosLogs();
    this.exibirRelatorioListView(titulo, logs);
  }

  /**
   * Método genérico para exibir relatório na tela
   */
  exibirRelatorio(titulo: string, conteudo: string): void {
    this.tituloRelatorio = titulo;
    this.conteudoRelatorio = conteudo;
    this.conteudoRelatorioFormatado = this.formatarRelatorioHTML(conteudo);
    this.dataGeracaoRelatorio = new Date().toLocaleString('pt-BR');
    this.exibirRelatorioNaTela = true;

    // Scroll para o relatório
    setTimeout(() => {
      const elemento = document.querySelector('.card.mt-3');
      if (elemento) {
        elemento.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }

  /**
   * Novo método para exibir relatório em ListView
   */
  exibirRelatorioListView(titulo: string, logs: any[]): void {
    this.tituloRelatorio = titulo;
    this.dataGeracaoRelatorio = new Date().toLocaleString('pt-BR');
    
    // Converter logs do serviço para o formato do ListView
    this.logsListView = this.converterLogsParaListView(logs);
    this.logsListViewFiltrados = [...this.logsListView];
    this.totalLogs = this.logsListView.length;
    
    // Calcular estatísticas
    this.calcularEstatisticas();
    
    this.exibirRelatorioNaTela = true;

    // Scroll para o relatório
    setTimeout(() => {
      const elemento = document.querySelector('.card.mt-3');
      if (elemento) {
        elemento.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }

  /**
   * Método para fechar relatório
   */
  fecharRelatorio(): void {
    this.exibirRelatorioNaTela = false;
    this.conteudoRelatorio = '';
    this.conteudoRelatorioFormatado = '';
    this.tituloRelatorio = '';
    this.dataGeracaoRelatorio = '';
    
    // Limpar dados do ListView
    this.logsListView = [];
    this.logsListViewFiltrados = [];
    this.filtroTipo = 'TODOS';
    this.filtroCategoria = 'TODAS';
    this.textoBusca = '';
    this.totalLogs = 0;
    this.estatisticas = {
      sucessos: 0,
      erros: 0,
      avisos: 0,
      processamentos: 0
    };
  }

  /**
   * Formata o relatório em HTML com cores e estilos
   */
  private formatarRelatorioHTML(conteudo: string): string {
    // Primeiro, escapa caracteres HTML especiais
    let html = conteudo
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');

    // Processa linhas individualmente para melhor controle
    const linhas = html.split('\n');
    const linhasProcessadas = linhas.map(linha => {
      let linhaProcessada = linha;

      // Detecta e formata diferentes tipos de logs com base no ícone de status
      if (linha.includes('✅')) {
        linhaProcessada = `<div class="log-success">${linha}</div>`;
      } else if (linha.includes('❌')) {
        linhaProcessada = `<div class="log-error">${linha}</div>`;
      } else if (linha.includes('⚠️')) {
        linhaProcessada = `<div class="log-warning">${linha}</div>`;
      } else if (linha.includes('🔄')) {
        linhaProcessada = `<div class="log-processing">${linha}</div>`;
      } else if (linha.includes('🔵')) {
        linhaProcessada = `<div class="log-info">${linha}</div>`;
      }
      // Headers e títulos
      else if (linha.includes('📈 RESUMO:')) {
        linhaProcessada = `<div class="log-header">${linha}</div>`;
      } else if (linha.includes('🔍 LOGS WHATSAPP')) {
        linhaProcessada = `<div class="log-title">${linha}</div>`;
      }
      // Separadores
      else if (/^═+$/.test(linha.trim())) {
        linhaProcessada = `<div class="log-separator">${linha}</div>`;
      } else if (/^─+$/.test(linha.trim())) {
        linhaProcessada = `<div class="log-divider">${linha}</div>`;
      } else if (/^┄+$/.test(linha.trim())) {
        linhaProcessada = `<div class="log-subdivider">${linha}</div>`;
      }
      // Linhas com informações específicas
      else if (linha.includes('⏰')) {
        linhaProcessada = `<span class="log-time">${linha}</span>`;
      } else if (linha.includes('👤')) {
        linhaProcessada = `<span class="log-user">${linha}</span>`;
      } else if (linha.includes('💬')) {
        linhaProcessada = `<span class="log-message">${linha}</span>`;
      } else if (linha.includes('📝')) {
        // Texto da mensagem - adiciona formatação especial
        linhaProcessada = `<span class="log-text-message">${linha}</span>`;
      } else if (linha.includes('📱')) {
        linhaProcessada = `<span class="log-phone">${linha}</span>`;
      } else if (linha.includes('⏱️')) {
        linhaProcessada = `<span class="log-duration">${linha}</span>`;
      } else if (linha.includes('🤖')) {
        linhaProcessada = `<span class="log-info">${linha}</span>`;
      } else if (linha.includes('🆔')) {
        linhaProcessada = `<span class="log-info">${linha}</span>`;
      } else if (linha.includes('📅')) {
        linhaProcessada = `<span class="log-info">${linha}</span>`;
      }

      return linhaProcessada;
    });

    // Junta as linhas processadas com <br> para manter quebras de linha
    const htmlFinal = linhasProcessadas.join('<br>');

    return `<div class="log-formatted">${htmlFinal}</div>`;
  }

  /**
   * Método para copiar relatório para área de transferência
   */
  copiarRelatorio(): void {
    let textoParaCopiar = '';
    
    if (this.logsListView.length > 0) {
      // Gerar texto a partir do ListView
      textoParaCopiar = this.gerarTextoDoListView();
    } else {
      // Usar conteúdo original como fallback
      textoParaCopiar = this.conteudoRelatorio;
    }
    
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(textoParaCopiar).then(() => {
        console.log('Relatório copiado para área de transferência');
        // Aqui você pode adicionar uma notificação de sucesso se quiser
      }).catch(err => {
        console.error('Erro ao copiar relatório:', err);
        this.copiarRelatorioFallback(textoParaCopiar);
      });
    } else {
      this.copiarRelatorioFallback(textoParaCopiar);
    }
  }
  
  /**
   * Gera texto formatado a partir dos logs do ListView
   */
  private gerarTextoDoListView(): string {
    let texto = `🔍 ${this.tituloRelatorio}\n`;
    texto += `📅 Gerado em: ${this.dataGeracaoRelatorio}\n`;
    texto += `📊 Total: ${this.totalLogs} logs\n`;
    texto += `${'═'.repeat(65)}\n\n`;
    
    // Estatísticas
    if (this.estatisticas.sucessos > 0) texto += `✅ ${this.estatisticas.sucessos} sucessos  `;
    if (this.estatisticas.erros > 0) texto += `❌ ${this.estatisticas.erros} erros  `;
    if (this.estatisticas.avisos > 0) texto += `⚠️ ${this.estatisticas.avisos} avisos  `;
    if (this.estatisticas.processamentos > 0) texto += `🔄 ${this.estatisticas.processamentos} processamentos`;
    texto += '\n\n';
    
    // Logs filtrados
    const logsParaExportar = this.logsListViewFiltrados.length > 0 ? this.logsListViewFiltrados : this.logsListView;
    
    logsParaExportar.forEach((log, index) => {
      texto += `${log.icone} ${log.iconeCategoria} ${log.categoria}\n`;
      texto += `⏰ ${log.timestamp.toLocaleTimeString('pt-BR')} (${log.tempoRelativo}) | 👤 ${log.nome}\n`;
      texto += `💬 ${log.mensagem}\n`;
      
      if (log.textoMensagem) {
        texto += `📝 Texto: "${log.textoMensagem}"\n`;
      }
      
      if (log.detalhesFormatados) {
        texto += `${log.detalhesFormatados}\n`;
      }
      
      if (index < logsParaExportar.length - 1) {
        texto += `${'─'.repeat(40)}\n\n`;
      }
    });
    
    return texto;
  }

  /**
   * Método fallback para copiar texto (navegadores mais antigos)
   */
  private copiarRelatorioFallback(texto?: string): void {
    const textArea = document.createElement('textarea');
    textArea.value = texto || this.conteudoRelatorio;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand('copy');
      console.log('Relatório copiado para área de transferência (fallback)');
    } catch (err) {
      console.error('Erro ao copiar relatório (fallback):', err);
    }

    document.body.removeChild(textArea);
  }

  /**
   * Método específico para exibir resultado do diagnóstico na tela
   */
  exibirResultadoDiagnosticoNaTela(diagnostico: any): void {
    let mensagem = `🔍 DIAGNÓSTICO COMPLETO\n`;
    mensagem += `📅 ${diagnostico.timestamp}\n\n`;

    mensagem += `👤 CONTATO:\n`;
    mensagem += `• Nome: ${diagnostico.contato.nome}\n`;
    mensagem += `• Telefone: ${diagnostico.contato.telefone}\n`;
    mensagem += `• Último contato: ${diagnostico.contato.ultimoContato}\n\n`;

    mensagem += `🤖 MIA:\n`;
    mensagem += `• Status: ${diagnostico.mia.status}\n`;
    mensagem += `• Ativa: ${diagnostico.mia.ativa}\n`;
    mensagem += `• Modo teste: ${diagnostico.mia.modoTeste}\n\n`;

    mensagem += `📱 WHATSAPP:\n`;
    mensagem += `• Número: ${diagnostico.whatsapp.numeroWhatsapp}\n`;
    mensagem += `• Carregou: ${diagnostico.whatsapp.carregouWhatsapp}\n`;
    mensagem += `• Em iframe: ${diagnostico.whatsapp.inIframe}\n\n`;

    mensagem += `⚙️ SISTEMA:\n`;
    mensagem += `• Componente carregado: ${diagnostico.sistema.carregou}\n`;
    mensagem += `• Modo widget: ${diagnostico.sistema.widget}\n`;
    mensagem += `• Modo desenvolvimento: ${diagnostico.sistema.dev}\n`;
    mensagem += `• Empresa: ${diagnostico.sistema.empresaNome} (ID: ${diagnostico.sistema.empresaId})\n`;
    mensagem += `• Usuário: ${diagnostico.sistema.usuarioNome} (ID: ${diagnostico.sistema.usuarioId})\n\n`;

    mensagem += `📋 LOGS WHATSAPP:\n`;
    mensagem += `• Total de logs: ${diagnostico.logsWhatsapp.totalLogs}\n`;
    mensagem += `• Último log: ${diagnostico.logsWhatsapp.ultimoLog}\n`;

    if (diagnostico.logsWhatsapp.estatisticas) {
      const stats = diagnostico.logsWhatsapp.estatisticas;
      mensagem += `• Sucessos: ${stats.porTipo.SUCCESS || 0}\n`;
      mensagem += `• Avisos: ${stats.porTipo.WARNING || 0}\n`;
      mensagem += `• Erros: ${stats.porTipo.ERROR || 0}\n`;
    }

    if (diagnostico.logsWhatsapp.logsRecentes.length > 0) {
      mensagem += `\n📝 LOGS RECENTES:\n`;
      diagnostico.logsWhatsapp.logsRecentes.forEach((log: any) => {
        const emoji = this.obterEmojiPorTipo(log.tipo);
        mensagem += `${emoji} [${log.timestamp}] ${log.mensagem}\n`;
      });
    }

    this.exibirRelatorio('🩺 Diagnóstico do Contato', mensagem);

    // Log no console para debug técnico
    console.log('🔍 Diagnóstico completo:', diagnostico);
  }

  ngOnDestroy(): void {
    this.assinante.unsubscribe();
  }

  /**
   * Converte logs do serviço para o formato do ListView
   */
  private converterLogsParaListView(logs: any[]): LogItem[] {
    return logs.map(log => {
      const icone = this.obterIconeStatus(log);
      const iconeCategoria = this.obterIconeCategoriaVisual(log.categoria);
      const cssClass = this.obterCssClass(log);
      const tempoRelativo = this.obterTempoRelativo(log.timestamp);
      const detalhesFormatados = this.formatarDetalhesParaListView(log.detalhes);

      return {
        timestamp: log.timestamp,
        tempoRelativo: tempoRelativo,
        tipo: log.tipo,
        categoria: log.categoria,
        icone: icone,
        iconeCategoria: iconeCategoria,
        nome: log.nome || 'Sistema',
        telefone: log.telefone || '',
        mensagem: log.mensagem,
        textoMensagem: log.textoMensagem,
        tipoMensagem: log.tipoMensagem,
        detalhes: log.detalhes,
        detalhesFormatados: detalhesFormatados,
        cssClass: cssClass,
        expanded: false
      };
    });
  }

  /**
   * Obtém ícone de status baseado no tipo e contexto do log
   */
  private obterIconeStatus(log: any): string {
    const mensagem = log.mensagem.toLowerCase();

    if (log.tipo === 'SUCCESS' || mensagem.includes('enviada com sucesso') || mensagem.includes('sucesso')) {
      return '✅';
    }

    if (log.tipo === 'ERROR' || mensagem.includes('erro') || mensagem.includes('falhou')) {
      return '❌';
    }

    if (log.tipo === 'WARNING' || mensagem.includes('bloqueada') || mensagem.includes('insuficiente') ||
        mensagem.includes('não vai enviar') || mensagem.includes('validação falhou')) {
      return '⚠️';
    }

    if (mensagem.includes('processando') || mensagem.includes('verificando') || mensagem.includes('iniciando')) {
      return '🔄';
    }

    return '🔵';
  }

  /**
   * Obtém ícone da categoria
   */
  private obterIconeCategoriaVisual(categoria: string): string {
    const icones = {
      'ENVIO': '📤',
      'VALIDACAO': '🔍',
      'PROCESSAMENTO': '⚙️',
      'SISTEMA': '🖥️'
    };
    return icones[categoria] || '📋';
  }

  /**
   * Obtém classe CSS baseada no tipo de log
   */
  private obterCssClass(log: any): string {
    const icone = this.obterIconeStatus(log);
    
    switch (icone) {
      case '✅': return 'log-success';
      case '❌': return 'log-error';
      case '⚠️': return 'log-warning';
      case '🔄': return 'log-processing';
      default: return 'log-info';
    }
  }

  /**
   * Calcula tempo relativo
   */
  private obterTempoRelativo(timestamp: Date): string {
    const agora = new Date();
    const diffMs = agora.getTime() - timestamp.getTime();
    const diffMinutos = Math.floor(diffMs / 60000);

    if (diffMinutos < 1) return 'agora';
    if (diffMinutos < 60) return `há ${diffMinutos}min`;

    const diffHoras = Math.floor(diffMinutos / 60);
    if (diffHoras < 24) return `há ${diffHoras}h`;

    const diffDias = Math.floor(diffHoras / 24);
    return `há ${diffDias}d`;
  }

  /**
   * Formata detalhes para exibição no ListView
   */
  private formatarDetalhesParaListView(detalhes: any): string {
    if (!detalhes || typeof detalhes !== 'object' || Object.keys(detalhes).length === 0) {
      return '';
    }

    let resultado = '';

    if (detalhes.tempoDecorrido !== undefined && detalhes.tempoNecessario !== undefined) {
      resultado += `⏱️ Tempo: ${detalhes.tempoDecorrido.toFixed(1)}h / ${detalhes.tempoNecessario.toFixed(1)}h necessárias\n`;
    }

    if (detalhes.statusMia) {
      resultado += `🤖 Mia: ${detalhes.statusMia}\n`;
    }

    if (detalhes.mensagemId) {
      resultado += `🆔 ID: ${detalhes.mensagemId}\n`;
    }

    if (detalhes.horarioEnvio) {
      resultado += `📅 Enviado: ${detalhes.horarioEnvio}\n`;
    }

    return resultado.trim();
  }

  /**
   * Calcula estatísticas dos logs
   */
  private calcularEstatisticas(): void {
    this.estatisticas = {
      sucessos: 0,
      erros: 0,
      avisos: 0,
      processamentos: 0
    };

    this.logsListView.forEach(log => {
      switch (log.icone) {
        case '✅':
          this.estatisticas.sucessos++;
          break;
        case '❌':
          this.estatisticas.erros++;
          break;
        case '⚠️':
          this.estatisticas.avisos++;
          break;
        case '🔄':
          this.estatisticas.processamentos++;
          break;
      }
    });
  }

  /**
   * Filtrar logs
   */
  filtrarLogs(): void {
    let logsFiltrados = [...this.logsListView];

    // Filtrar por tipo
    if (this.filtroTipo !== 'TODOS') {
      logsFiltrados = logsFiltrados.filter(log => log.tipo === this.filtroTipo);
    }

    // Filtrar por categoria
    if (this.filtroCategoria !== 'TODAS') {
      logsFiltrados = logsFiltrados.filter(log => log.categoria === this.filtroCategoria);
    }

    // Filtrar por texto de busca
    if (this.textoBusca && this.textoBusca.trim()) {
      const textoBuscaLower = this.textoBusca.toLowerCase();
      logsFiltrados = logsFiltrados.filter(log => 
        log.mensagem.toLowerCase().includes(textoBuscaLower) ||
        (log.textoMensagem && log.textoMensagem.toLowerCase().includes(textoBuscaLower)) ||
        log.nome.toLowerCase().includes(textoBuscaLower) ||
        log.telefone.includes(textoBuscaLower)
      );
    }

    this.logsListViewFiltrados = logsFiltrados;
  }

  /**
   * Expandir/colapsar detalhes do log
   */
  toggleDetalhes(item: LogItem): void {
    item.expanded = !item.expanded;
  }

  /**
   * Limpar filtros
   */
  limparFiltros(): void {
    this.filtroTipo = 'TODOS';
    this.filtroCategoria = 'TODAS';
    this.textoBusca = '';
    this.logsListViewFiltrados = [...this.logsListView];
  }
}
